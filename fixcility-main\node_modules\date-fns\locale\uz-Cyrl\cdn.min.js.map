{"version": 3, "sources": ["lib/locale/uz-Cyrl/cdn.js"], "sourcesContent": ["(() => {\nvar _window$dateFns;function _typeof(o) {\"@babel/helpers - typeof\";return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {return typeof o;} : function (o) {return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;}, _typeof(o);}function ownKeys(e, r) {var t = Object.keys(e);if (Object.getOwnPropertySymbols) {var o = Object.getOwnPropertySymbols(e);r && (o = o.filter(function (r) {return Object.getOwnPropertyDescriptor(e, r).enumerable;})), t.push.apply(t, o);}return t;}function _objectSpread(e) {for (var r = 1; r < arguments.length; r++) {var t = null != arguments[r] ? arguments[r] : {};r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {_defineProperty(e, r, t[r]);}) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));});}return e;}function _defineProperty(obj, key, value) {key = _toPropertyKey(key);if (key in obj) {Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true });} else {obj[key] = value;}return obj;}function _toPropertyKey(t) {var i = _toPrimitive(t, \"string\");return \"symbol\" == _typeof(i) ? i : String(i);}function _toPrimitive(t, r) {if (\"object\" != _typeof(t) || !t) return t;var e = t[Symbol.toPrimitive];if (void 0 !== e) {var i = e.call(t, r || \"default\");if (\"object\" != _typeof(i)) return i;throw new TypeError(\"@@toPrimitive must return a primitive value.\");}return (\"string\" === r ? String : Number)(t);}var __defProp = Object.defineProperty;\nvar __export = function __export(target, all) {\n  for (var name in all)\n  __defProp(target, name, {\n    get: all[name],\n    enumerable: true,\n    configurable: true,\n    set: function set(newValue) {return all[name] = function () {return newValue;};}\n  });\n};\n\n// lib/locale/uz-Cyrl/_lib/formatDistance.js\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"1 \\u0441\\u043E\\u043D\\u0438\\u044F\\u0434\\u0430\\u043D \\u043A\\u0430\\u043C\",\n    other: \"{{count}} \\u0441\\u043E\\u043D\\u0438\\u044F\\u0434\\u0430\\u043D \\u043A\\u0430\\u043C\"\n  },\n  xSeconds: {\n    one: \"1 \\u0441\\u043E\\u043D\\u0438\\u044F\",\n    other: \"{{count}} \\u0441\\u043E\\u043D\\u0438\\u044F\"\n  },\n  halfAMinute: \"\\u044F\\u0440\\u0438\\u043C \\u0434\\u0430\\u049B\\u0438\\u049B\\u0430\",\n  lessThanXMinutes: {\n    one: \"1 \\u0434\\u0430\\u049B\\u0438\\u049B\\u0430\\u0434\\u0430\\u043D \\u043A\\u0430\\u043C\",\n    other: \"{{count}} \\u0434\\u0430\\u049B\\u0438\\u049B\\u0430\\u0434\\u0430\\u043D \\u043A\\u0430\\u043C\"\n  },\n  xMinutes: {\n    one: \"1 \\u0434\\u0430\\u049B\\u0438\\u049B\\u0430\",\n    other: \"{{count}} \\u0434\\u0430\\u049B\\u0438\\u049B\\u0430\"\n  },\n  aboutXHours: {\n    one: \"\\u0442\\u0430\\u0445\\u043C\\u0438\\u043D\\u0430\\u043D 1 \\u0441\\u043E\\u0430\\u0442\",\n    other: \"\\u0442\\u0430\\u0445\\u043C\\u0438\\u043D\\u0430\\u043D {{count}} \\u0441\\u043E\\u0430\\u0442\"\n  },\n  xHours: {\n    one: \"1 \\u0441\\u043E\\u0430\\u0442\",\n    other: \"{{count}} \\u0441\\u043E\\u0430\\u0442\"\n  },\n  xDays: {\n    one: \"1 \\u043A\\u0443\\u043D\",\n    other: \"{{count}} \\u043A\\u0443\\u043D\"\n  },\n  aboutXWeeks: {\n    one: \"\\u0442\\u0430\\u0445\\u043C\\u0438\\u043D\\u0430\\u043D 1 \\u0445\\u0430\\u0444\\u0442\\u0430\",\n    other: \"\\u0442\\u0430\\u0445\\u043C\\u0438\\u043D\\u0430\\u043D {{count}} \\u0445\\u0430\\u0444\\u0442\\u0430\"\n  },\n  xWeeks: {\n    one: \"1 \\u0445\\u0430\\u0444\\u0442\\u0430\",\n    other: \"{{count}} \\u0445\\u0430\\u0444\\u0442\\u0430\"\n  },\n  aboutXMonths: {\n    one: \"\\u0442\\u0430\\u0445\\u043C\\u0438\\u043D\\u0430\\u043D 1 \\u043E\\u0439\",\n    other: \"\\u0442\\u0430\\u0445\\u043C\\u0438\\u043D\\u0430\\u043D {{count}} \\u043E\\u0439\"\n  },\n  xMonths: {\n    one: \"1 \\u043E\\u0439\",\n    other: \"{{count}} \\u043E\\u0439\"\n  },\n  aboutXYears: {\n    one: \"\\u0442\\u0430\\u0445\\u043C\\u0438\\u043D\\u0430\\u043D 1 \\u0439\\u0438\\u043B\",\n    other: \"\\u0442\\u0430\\u0445\\u043C\\u0438\\u043D\\u0430\\u043D {{count}} \\u0439\\u0438\\u043B\"\n  },\n  xYears: {\n    one: \"1 \\u0439\\u0438\\u043B\",\n    other: \"{{count}} \\u0439\\u0438\\u043B\"\n  },\n  overXYears: {\n    one: \"1 \\u0439\\u0438\\u043B\\u0434\\u0430\\u043D \\u043A\\u045E\\u043F\",\n    other: \"{{count}} \\u0439\\u0438\\u043B\\u0434\\u0430\\u043D \\u043A\\u045E\\u043F\"\n  },\n  almostXYears: {\n    one: \"\\u0434\\u0435\\u044F\\u0440\\u043B\\u0438 1 \\u0439\\u0438\\u043B\",\n    other: \"\\u0434\\u0435\\u044F\\u0440\\u043B\\u0438 {{count}} \\u0439\\u0438\\u043B\"\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \"\\u0434\\u0430\\u043D \\u043A\\u0435\\u0439\\u0438\\u043D\";\n    } else {\n      return result + \" \\u043E\\u043B\\u0434\\u0438\\u043D\";\n    }\n  }\n  return result;\n};\n\n// lib/locale/_lib/buildFormatLongFn.js\nfunction buildFormatLongFn(args) {\n  return function () {var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var width = options.width ? String(options.width) : args.defaultWidth;\n    var format = args.formats[width] || args.formats[args.defaultWidth];\n    return format;\n  };\n}\n\n// lib/locale/uz-Cyrl/_lib/formatLong.js\nvar dateFormats = {\n  full: \"EEEE, do MMMM, y\",\n  long: \"do MMMM, y\",\n  medium: \"d MMM, y\",\n  short: \"dd/MM/yyyy\"\n};\nvar timeFormats = {\n  full: \"H:mm:ss zzzz\",\n  long: \"H:mm:ss z\",\n  medium: \"H:mm:ss\",\n  short: \"H:mm\"\n};\nvar dateTimeFormats = {\n  any: \"{{date}}, {{time}}\"\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\"\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\"\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"any\"\n  })\n};\n\n// lib/locale/uz-Cyrl/_lib/formatRelative.js\nvar formatRelativeLocale = {\n  lastWeek: \"'\\u045E\\u0442\\u0433\\u0430\\u043D' eeee p '\\u0434\\u0430'\",\n  yesterday: \"'\\u043A\\u0435\\u0447\\u0430' p '\\u0434\\u0430'\",\n  today: \"'\\u0431\\u0443\\u0433\\u0443\\u043D' p '\\u0434\\u0430'\",\n  tomorrow: \"'\\u044D\\u0440\\u0442\\u0430\\u0433\\u0430' p '\\u0434\\u0430'\",\n  nextWeek: \"eeee p '\\u0434\\u0430'\",\n  other: \"P\"\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {return formatRelativeLocale[token];};\n\n// lib/locale/_lib/buildLocalizeFn.js\nfunction buildLocalizeFn(args) {\n  return function (value, options) {\n    var context = options !== null && options !== void 0 && options.context ? String(options.context) : \"standalone\";\n    var valuesArray;\n    if (context === \"formatting\" && args.formattingValues) {\n      var defaultWidth = args.defaultFormattingWidth || args.defaultWidth;\n      var width = options !== null && options !== void 0 && options.width ? String(options.width) : defaultWidth;\n      valuesArray = args.formattingValues[width] || args.formattingValues[defaultWidth];\n    } else {\n      var _defaultWidth = args.defaultWidth;\n      var _width = options !== null && options !== void 0 && options.width ? String(options.width) : args.defaultWidth;\n      valuesArray = args.values[_width] || args.values[_defaultWidth];\n    }\n    var index = args.argumentCallback ? args.argumentCallback(value) : value;\n    return valuesArray[index];\n  };\n}\n\n// lib/locale/uz-Cyrl/_lib/localize.js\nvar eraValues = {\n  narrow: [\"\\u041C.\\u0410\", \"\\u041C\"],\n  abbreviated: [\"\\u041C.\\u0410\", \"\\u041C\"],\n  wide: [\"\\u041C\\u0438\\u043B\\u043E\\u0434\\u0434\\u0430\\u043D \\u0410\\u0432\\u0432\\u0430\\u043B\\u0433\\u0438\", \"\\u041C\\u0438\\u043B\\u043E\\u0434\\u0438\\u0439\"]\n};\nvar quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-\\u0447\\u043E\\u0440.\", \"2-\\u0447\\u043E\\u0440.\", \"3-\\u0447\\u043E\\u0440.\", \"4-\\u0447\\u043E\\u0440.\"],\n  wide: [\"1-\\u0447\\u043E\\u0440\\u0430\\u043A\", \"2-\\u0447\\u043E\\u0440\\u0430\\u043A\", \"3-\\u0447\\u043E\\u0440\\u0430\\u043A\", \"4-\\u0447\\u043E\\u0440\\u0430\\u043A\"]\n};\nvar monthValues = {\n  narrow: [\"\\u042F\", \"\\u0424\", \"\\u041C\", \"\\u0410\", \"\\u041C\", \"\\u0418\", \"\\u0418\", \"\\u0410\", \"\\u0421\", \"\\u041E\", \"\\u041D\", \"\\u0414\"],\n  abbreviated: [\n  \"\\u044F\\u043D\\u0432\",\n  \"\\u0444\\u0435\\u0432\",\n  \"\\u043C\\u0430\\u0440\",\n  \"\\u0430\\u043F\\u0440\",\n  \"\\u043C\\u0430\\u0439\",\n  \"\\u0438\\u044E\\u043D\",\n  \"\\u0438\\u044E\\u043B\",\n  \"\\u0430\\u0432\\u0433\",\n  \"\\u0441\\u0435\\u043D\",\n  \"\\u043E\\u043A\\u0442\",\n  \"\\u043D\\u043E\\u044F\",\n  \"\\u0434\\u0435\\u043A\"],\n\n  wide: [\n  \"\\u044F\\u043D\\u0432\\u0430\\u0440\",\n  \"\\u0444\\u0435\\u0432\\u0440\\u0430\\u043B\",\n  \"\\u043C\\u0430\\u0440\\u0442\",\n  \"\\u0430\\u043F\\u0440\\u0435\\u043B\",\n  \"\\u043C\\u0430\\u0439\",\n  \"\\u0438\\u044E\\u043D\",\n  \"\\u0438\\u044E\\u043B\",\n  \"\\u0430\\u0432\\u0433\\u0443\\u0441\\u0442\",\n  \"\\u0441\\u0435\\u043D\\u0442\\u0430\\u0431\\u0440\",\n  \"\\u043E\\u043A\\u0442\\u0430\\u0431\\u0440\",\n  \"\\u043D\\u043E\\u044F\\u0431\\u0440\",\n  \"\\u0434\\u0435\\u043A\\u0430\\u0431\\u0440\"]\n\n};\nvar dayValues = {\n  narrow: [\"\\u042F\", \"\\u0414\", \"\\u0421\", \"\\u0427\", \"\\u041F\", \"\\u0416\", \"\\u0428\"],\n  short: [\"\\u044F\\u043A\", \"\\u0434\\u0443\", \"\\u0441\\u0435\", \"\\u0447\\u043E\", \"\\u043F\\u0430\", \"\\u0436\\u0443\", \"\\u0448\\u0430\"],\n  abbreviated: [\"\\u044F\\u043A\\u0448\", \"\\u0434\\u0443\\u0448\", \"\\u0441\\u0435\\u0448\", \"\\u0447\\u043E\\u0440\", \"\\u043F\\u0430\\u0439\", \"\\u0436\\u0443\\u043C\", \"\\u0448\\u0430\\u043D\"],\n  wide: [\n  \"\\u044F\\u043A\\u0448\\u0430\\u043D\\u0431\\u0430\",\n  \"\\u0434\\u0443\\u0448\\u0430\\u043D\\u0431\\u0430\",\n  \"\\u0441\\u0435\\u0448\\u0430\\u043D\\u0431\\u0430\",\n  \"\\u0447\\u043E\\u0440\\u0448\\u0430\\u043D\\u0431\\u0430\",\n  \"\\u043F\\u0430\\u0439\\u0448\\u0430\\u043D\\u0431\\u0430\",\n  \"\\u0436\\u0443\\u043C\\u0430\",\n  \"\\u0448\\u0430\\u043D\\u0431\\u0430\"]\n\n};\nvar dayPeriodValues = {\n  any: {\n    am: \"\\u041F.\\u041E.\",\n    pm: \"\\u041F.\\u041A.\",\n    midnight: \"\\u044F\\u0440\\u0438\\u043C \\u0442\\u0443\\u043D\",\n    noon: \"\\u043F\\u0435\\u0448\\u0438\\u043D\",\n    morning: \"\\u044D\\u0440\\u0442\\u0430\\u043B\\u0430\\u0431\",\n    afternoon: \"\\u043F\\u0435\\u0448\\u0438\\u043D\\u0434\\u0430\\u043D \\u043A\\u0435\\u0439\\u0438\\u043D\",\n    evening: \"\\u043A\\u0435\\u0447\\u0430\\u0441\\u0438\",\n    night: \"\\u0442\\u0443\\u043D\"\n  }\n};\nvar formattingDayPeriodValues = {\n  any: {\n    am: \"\\u041F.\\u041E.\",\n    pm: \"\\u041F.\\u041A.\",\n    midnight: \"\\u044F\\u0440\\u0438\\u043C \\u0442\\u0443\\u043D\",\n    noon: \"\\u043F\\u0435\\u0448\\u0438\\u043D\",\n    morning: \"\\u044D\\u0440\\u0442\\u0430\\u043B\\u0430\\u0431\",\n    afternoon: \"\\u043F\\u0435\\u0448\\u0438\\u043D\\u0434\\u0430\\u043D \\u043A\\u0435\\u0439\\u0438\\u043D\",\n    evening: \"\\u043A\\u0435\\u0447\\u0430\\u0441\\u0438\",\n    night: \"\\u0442\\u0443\\u043D\"\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  return String(dirtyNumber);\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: function argumentCallback(quarter) {return quarter - 1;}\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"any\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"any\"\n  })\n};\n\n// lib/locale/_lib/buildMatchFn.js\nfunction buildMatchFn(args) {\n  return function (string) {var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var width = options.width;\n    var matchPattern = width && args.matchPatterns[width] || args.matchPatterns[args.defaultMatchWidth];\n    var matchResult = string.match(matchPattern);\n    if (!matchResult) {\n      return null;\n    }\n    var matchedString = matchResult[0];\n    var parsePatterns = width && args.parsePatterns[width] || args.parsePatterns[args.defaultParseWidth];\n    var key = Array.isArray(parsePatterns) ? findIndex(parsePatterns, function (pattern) {return pattern.test(matchedString);}) : findKey(parsePatterns, function (pattern) {return pattern.test(matchedString);});\n    var value;\n    value = args.valueCallback ? args.valueCallback(key) : key;\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    var rest = string.slice(matchedString.length);\n    return { value: value, rest: rest };\n  };\n}\nfunction findKey(object, predicate) {\n  for (var key in object) {\n    if (Object.prototype.hasOwnProperty.call(object, key) && predicate(object[key])) {\n      return key;\n    }\n  }\n  return;\n}\nfunction findIndex(array, predicate) {\n  for (var key = 0; key < array.length; key++) {\n    if (predicate(array[key])) {\n      return key;\n    }\n  }\n  return;\n}\n\n// lib/locale/_lib/buildMatchPatternFn.js\nfunction buildMatchPatternFn(args) {\n  return function (string) {var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var matchResult = string.match(args.matchPattern);\n    if (!matchResult)\n    return null;\n    var matchedString = matchResult[0];\n    var parseResult = string.match(args.parsePattern);\n    if (!parseResult)\n    return null;\n    var value = args.valueCallback ? args.valueCallback(parseResult[0]) : parseResult[0];\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    var rest = string.slice(matchedString.length);\n    return { value: value, rest: rest };\n  };\n}\n\n// lib/locale/uz-Cyrl/_lib/match.js\nvar matchOrdinalNumberPattern = /^(\\d+)(чи)?/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^(м\\.а|м\\.)/i,\n  abbreviated: /^(м\\.а|м\\.)/i,\n  wide: /^(милоддан аввал|милоддан кейин)/i\n};\nvar parseEraPatterns = {\n  any: [/^м/i, /^а/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^[1234]-чор./i,\n  wide: /^[1234]-чорак/i\n};\nvar parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^[яфмамииасонд]/i,\n  abbreviated: /^(янв|фев|мар|апр|май|июн|июл|авг|сен|окт|ноя|дек)/i,\n  wide: /^(январ|феврал|март|апрел|май|июн|июл|август|сентабр|октабр|ноябр|декабр)/i\n};\nvar parseMonthPatterns = {\n  narrow: [\n  /^я/i,\n  /^ф/i,\n  /^м/i,\n  /^а/i,\n  /^м/i,\n  /^и/i,\n  /^и/i,\n  /^а/i,\n  /^с/i,\n  /^о/i,\n  /^н/i,\n  /^д/i],\n\n  any: [\n  /^я/i,\n  /^ф/i,\n  /^мар/i,\n  /^ап/i,\n  /^май/i,\n  /^июн/i,\n  /^июл/i,\n  /^ав/i,\n  /^с/i,\n  /^о/i,\n  /^н/i,\n  /^д/i]\n\n};\nvar matchDayPatterns = {\n  narrow: /^[ядсчпжш]/i,\n  short: /^(як|ду|се|чо|па|жу|ша)/i,\n  abbreviated: /^(якш|душ|сеш|чор|пай|жум|шан)/i,\n  wide: /^(якшанба|душанба|сешанба|чоршанба|пайшанба|жума|шанба)/i\n};\nvar parseDayPatterns = {\n  narrow: [/^я/i, /^д/i, /^с/i, /^ч/i, /^п/i, /^ж/i, /^ш/i],\n  any: [/^як/i, /^ду/i, /^се/i, /^чор/i, /^пай/i, /^жу/i, /^шан/i]\n};\nvar matchDayPeriodPatterns = {\n  any: /^(п\\.о\\.|п\\.к\\.|ярим тун|пешиндан кейин|(эрталаб|пешиндан кейин|кечаси|тун))/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^п\\.о\\./i,\n    pm: /^п\\.к\\./i,\n    midnight: /^ярим тун/i,\n    noon: /^пешиндан кейин/i,\n    morning: /эрталаб/i,\n    afternoon: /пешиндан кейин/i,\n    evening: /кечаси/i,\n    night: /тун/i\n  }\n};\nvar match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {return parseInt(value, 10);}\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: function valueCallback(index) {return index + 1;}\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"any\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\"\n  })\n};\n\n// lib/locale/uz-Cyrl.js\nvar uzCyrl = {\n  code: \"uz-Cyrl\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1,\n    firstWeekContainsDate: 1\n  }\n};\n\n// lib/locale/uz-Cyrl/cdn.js\nwindow.dateFns = _objectSpread(_objectSpread({},\nwindow.dateFns), {}, {\n  locale: _objectSpread(_objectSpread({}, (_window$dateFns =\n  window.dateFns) === null || _window$dateFns === void 0 ? void 0 : _window$dateFns.locale), {}, {\n    uzCyrl: uzCyrl }) });\n\n\n\n//# debugId=749AC72AA08BCA4264756E2164756E21\n\n//# sourceMappingURL=cdn.js.map\n})();"], "mappings": "AAAA,CAAC,IAAM,CACP,IAAI,EAAgB,SAAS,CAAO,CAAC,EAAG,CAA2B,OAAO,SAA+B,QAArB,mBAAkD,OAAO,UAA1B,iBAA8C,CAAC,EAAG,CAAC,cAAc,WAAe,CAAC,EAAG,CAAC,OAAO,UAA0B,QAArB,YAA+B,EAAE,cAAgB,QAAU,IAAM,OAAO,UAAY,gBAAkB,GAAK,EAAQ,CAAC,EAAG,SAAS,CAAO,CAAC,EAAG,EAAG,CAAC,IAAI,EAAI,OAAO,KAAK,CAAC,EAAE,GAAI,OAAO,sBAAuB,CAAC,IAAI,EAAI,OAAO,sBAAsB,CAAC,EAAE,IAAM,EAAI,EAAE,eAAgB,CAAC,EAAG,CAAC,OAAO,OAAO,yBAAyB,EAAG,CAAC,EAAE,WAAY,GAAI,EAAE,KAAK,MAAM,EAAG,CAAC,EAAG,OAAO,EAAG,SAAS,CAAa,CAAC,EAAG,CAAC,QAAS,EAAI,EAAG,EAAI,UAAU,OAAQ,IAAK,CAAC,IAAI,EAAY,UAAU,IAAlB,KAAuB,UAAU,GAAK,CAAC,EAAE,EAAI,EAAI,EAAQ,OAAO,CAAC,EAAG,EAAE,EAAE,gBAAiB,CAAC,EAAG,CAAC,EAAgB,EAAG,EAAG,EAAE,EAAE,EAAG,EAAI,OAAO,0BAA4B,OAAO,iBAAiB,EAAG,OAAO,0BAA0B,CAAC,CAAC,EAAI,EAAQ,OAAO,CAAC,CAAC,EAAE,gBAAiB,CAAC,EAAG,CAAC,OAAO,eAAe,EAAG,EAAG,OAAO,yBAAyB,EAAG,CAAC,CAAC,EAAG,EAAG,OAAO,EAAG,SAAS,CAAe,CAAC,EAAK,EAAK,EAAO,CAA2B,GAA1B,EAAM,EAAe,CAAG,EAAM,KAAO,EAAM,OAAO,eAAe,EAAK,EAAK,CAAE,MAAO,EAAO,WAAY,GAAM,aAAc,GAAM,SAAU,EAAK,CAAC,MAAU,GAAI,GAAO,EAAO,OAAO,EAAK,SAAS,CAAc,CAAC,EAAG,CAAC,IAAI,EAAI,EAAa,EAAG,QAAQ,EAAE,OAAmB,EAAQ,CAAC,GAArB,SAAyB,EAAI,OAAO,CAAC,EAAG,SAAS,CAAY,CAAC,EAAG,EAAG,CAAC,GAAgB,EAAQ,CAAC,GAArB,WAA2B,EAAG,OAAO,EAAE,IAAI,EAAI,EAAE,OAAO,aAAa,GAAe,IAAN,OAAS,CAAC,IAAI,EAAI,EAAE,KAAK,EAAG,GAAK,SAAS,EAAE,GAAgB,EAAQ,CAAC,GAArB,SAAwB,OAAO,EAAE,MAAM,IAAI,UAAU,8CAA8C,EAAG,OAAqB,IAAb,SAAiB,OAAS,QAAQ,CAAC,EAAG,IAAI,EAAY,OAAO,eACroD,YAAoB,CAAQ,CAAC,EAAQ,EAAK,CAC5C,QAAS,KAAQ,EACjB,EAAU,EAAQ,EAAM,CACtB,IAAK,EAAI,GACT,WAAY,GACZ,aAAc,GACd,aAAc,CAAG,CAAC,EAAU,CAAC,OAAO,EAAI,WAAiB,EAAG,CAAC,OAAO,GACtE,CAAC,GAIC,EAAuB,CACzB,iBAAkB,CAChB,IAAK,wEACL,MAAO,+EACT,EACA,SAAU,CACR,IAAK,mCACL,MAAO,0CACT,EACA,YAAa,gEACb,iBAAkB,CAChB,IAAK,8EACL,MAAO,qFACT,EACA,SAAU,CACR,IAAK,yCACL,MAAO,gDACT,EACA,YAAa,CACX,IAAK,8EACL,MAAO,qFACT,EACA,OAAQ,CACN,IAAK,6BACL,MAAO,oCACT,EACA,MAAO,CACL,IAAK,uBACL,MAAO,8BACT,EACA,YAAa,CACX,IAAK,oFACL,MAAO,2FACT,EACA,OAAQ,CACN,IAAK,mCACL,MAAO,0CACT,EACA,aAAc,CACZ,IAAK,kEACL,MAAO,yEACT,EACA,QAAS,CACP,IAAK,iBACL,MAAO,wBACT,EACA,YAAa,CACX,IAAK,wEACL,MAAO,+EACT,EACA,OAAQ,CACN,IAAK,uBACL,MAAO,8BACT,EACA,WAAY,CACV,IAAK,4DACL,MAAO,mEACT,EACA,aAAc,CACZ,IAAK,4DACL,MAAO,mEACT,CACF,EACI,WAA0B,CAAc,CAAC,EAAO,EAAO,EAAS,CAClE,IAAI,EACA,EAAa,EAAqB,GACtC,UAAW,IAAe,SACxB,EAAS,UACA,IAAU,EACnB,EAAS,EAAW,QAEpB,GAAS,EAAW,MAAM,QAAQ,YAAa,OAAO,CAAK,CAAC,EAE9D,GAAI,IAAY,MAAQ,IAAiB,QAAK,EAAQ,UACpD,GAAI,EAAQ,YAAc,EAAQ,WAAa,EAC7C,OAAO,EAAS,wDAEhB,QAAO,EAAS,kCAGpB,OAAO,GAIT,SAAS,CAAiB,CAAC,EAAM,CAC/B,eAAgB,EAAG,CAAC,IAAI,EAAU,UAAU,OAAS,GAAK,UAAU,KAAO,OAAY,UAAU,GAAK,CAAC,EACjG,EAAQ,EAAQ,MAAQ,OAAO,EAAQ,KAAK,EAAI,EAAK,aACrD,EAAS,EAAK,QAAQ,IAAU,EAAK,QAAQ,EAAK,cACtD,OAAO,GAKX,IAAI,EAAc,CAChB,KAAM,mBACN,KAAM,aACN,OAAQ,WACR,MAAO,YACT,EACI,EAAc,CAChB,KAAM,eACN,KAAM,YACN,OAAQ,UACR,MAAO,MACT,EACI,EAAkB,CACpB,IAAK,oBACP,EACI,EAAa,CACf,KAAM,EAAkB,CACtB,QAAS,EACT,aAAc,MAChB,CAAC,EACD,KAAM,EAAkB,CACtB,QAAS,EACT,aAAc,MAChB,CAAC,EACD,SAAU,EAAkB,CAC1B,QAAS,EACT,aAAc,KAChB,CAAC,CACH,EAGI,EAAuB,CACzB,SAAU,yDACV,UAAW,8CACX,MAAO,oDACP,SAAU,0DACV,SAAU,wBACV,MAAO,GACT,EACI,WAA0B,CAAc,CAAC,EAAO,EAAO,EAAW,EAAU,CAAC,OAAO,EAAqB,IAG7G,SAAS,CAAe,CAAC,EAAM,CAC7B,eAAgB,CAAC,EAAO,EAAS,CAC/B,IAAI,EAAU,IAAY,MAAQ,IAAiB,QAAK,EAAQ,QAAU,OAAO,EAAQ,OAAO,EAAI,aAChG,EACJ,GAAI,IAAY,cAAgB,EAAK,iBAAkB,CACrD,IAAI,EAAe,EAAK,wBAA0B,EAAK,aACnD,EAAQ,IAAY,MAAQ,IAAiB,QAAK,EAAQ,MAAQ,OAAO,EAAQ,KAAK,EAAI,EAC9F,EAAc,EAAK,iBAAiB,IAAU,EAAK,iBAAiB,OAC/D,CACL,IAAI,EAAgB,EAAK,aACrB,EAAS,IAAY,MAAQ,IAAiB,QAAK,EAAQ,MAAQ,OAAO,EAAQ,KAAK,EAAI,EAAK,aACpG,EAAc,EAAK,OAAO,IAAW,EAAK,OAAO,GAEnD,IAAI,EAAQ,EAAK,iBAAmB,EAAK,iBAAiB,CAAK,EAAI,EACnE,OAAO,EAAY,IAKvB,IAAI,EAAY,CACd,OAAQ,CAAC,gBAAiB,QAAQ,EAClC,YAAa,CAAC,gBAAiB,QAAQ,EACvC,KAAM,CAAC,8FAA+F,4CAA4C,CACpJ,EACI,EAAgB,CAClB,OAAQ,CAAC,IAAK,IAAK,IAAK,GAAG,EAC3B,YAAa,CAAC,wBAAyB,wBAAyB,wBAAyB,uBAAuB,EAChH,KAAM,CAAC,mCAAoC,mCAAoC,mCAAoC,kCAAkC,CACvJ,EACI,EAAc,CAChB,OAAQ,CAAC,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,QAAQ,EAC/H,YAAa,CACb,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,oBAAoB,EAEpB,KAAM,CACN,iCACA,uCACA,2BACA,iCACA,qBACA,qBACA,qBACA,uCACA,6CACA,uCACA,iCACA,sCAAsC,CAExC,EACI,EAAY,CACd,OAAQ,CAAC,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,QAAQ,EAC7E,MAAO,CAAC,eAAgB,eAAgB,eAAgB,eAAgB,eAAgB,eAAgB,cAAc,EACtH,YAAa,CAAC,qBAAsB,qBAAsB,qBAAsB,qBAAsB,qBAAsB,qBAAsB,oBAAoB,EACtK,KAAM,CACN,6CACA,6CACA,6CACA,mDACA,mDACA,2BACA,gCAAgC,CAElC,EACI,EAAkB,CACpB,IAAK,CACH,GAAI,iBACJ,GAAI,iBACJ,SAAU,8CACV,KAAM,iCACN,QAAS,6CACT,UAAW,kFACX,QAAS,uCACT,MAAO,oBACT,CACF,EACI,EAA4B,CAC9B,IAAK,CACH,GAAI,iBACJ,GAAI,iBACJ,SAAU,8CACV,KAAM,iCACN,QAAS,6CACT,UAAW,kFACX,QAAS,uCACT,MAAO,oBACT,CACF,EACI,WAAyB,CAAa,CAAC,EAAa,EAAU,CAChE,OAAO,OAAO,CAAW,GAEvB,EAAW,CACb,cAAe,EACf,IAAK,EAAgB,CACnB,OAAQ,EACR,aAAc,MAChB,CAAC,EACD,QAAS,EAAgB,CACvB,OAAQ,EACR,aAAc,OACd,0BAA2B,CAAgB,CAAC,EAAS,CAAC,OAAO,EAAU,EACzE,CAAC,EACD,MAAO,EAAgB,CACrB,OAAQ,EACR,aAAc,MAChB,CAAC,EACD,IAAK,EAAgB,CACnB,OAAQ,EACR,aAAc,MAChB,CAAC,EACD,UAAW,EAAgB,CACzB,OAAQ,EACR,aAAc,MACd,iBAAkB,EAClB,uBAAwB,KAC1B,CAAC,CACH,EAGA,SAAS,CAAY,CAAC,EAAM,CAC1B,eAAgB,CAAC,EAAQ,CAAC,IAAI,EAAU,UAAU,OAAS,GAAK,UAAU,KAAO,OAAY,UAAU,GAAK,CAAC,EACvG,EAAQ,EAAQ,MAChB,EAAe,GAAS,EAAK,cAAc,IAAU,EAAK,cAAc,EAAK,mBAC7E,EAAc,EAAO,MAAM,CAAY,EAC3C,IAAK,EACH,OAAO,KAET,IAAI,EAAgB,EAAY,GAC5B,EAAgB,GAAS,EAAK,cAAc,IAAU,EAAK,cAAc,EAAK,mBAC9E,EAAM,MAAM,QAAQ,CAAa,EAAI,EAAU,UAAwB,CAAC,EAAS,CAAC,OAAO,EAAQ,KAAK,CAAa,EAAG,EAAI,EAAQ,UAAwB,CAAC,EAAS,CAAC,OAAO,EAAQ,KAAK,CAAa,EAAG,EACzM,EACJ,EAAQ,EAAK,cAAgB,EAAK,cAAc,CAAG,EAAI,EACvD,EAAQ,EAAQ,cAAgB,EAAQ,cAAc,CAAK,EAAI,EAC/D,IAAI,GAAO,EAAO,MAAM,EAAc,MAAM,EAC5C,MAAO,CAAE,MAAO,EAAO,KAAM,EAAK,GAGtC,SAAS,CAAO,CAAC,EAAQ,EAAW,CAClC,QAAS,KAAO,EACd,GAAI,OAAO,UAAU,eAAe,KAAK,EAAQ,CAAG,GAAK,EAAU,EAAO,EAAI,EAC5E,OAAO,EAGX,OAEF,SAAS,CAAS,CAAC,EAAO,EAAW,CACnC,QAAS,EAAM,EAAG,EAAM,EAAM,OAAQ,IACpC,GAAI,EAAU,EAAM,EAAI,EACtB,OAAO,EAGX,OAIF,SAAS,CAAmB,CAAC,EAAM,CACjC,eAAgB,CAAC,EAAQ,CAAC,IAAI,EAAU,UAAU,OAAS,GAAK,UAAU,KAAO,OAAY,UAAU,GAAK,CAAC,EACvG,EAAc,EAAO,MAAM,EAAK,YAAY,EAChD,IAAK,EACL,OAAO,KACP,IAAI,EAAgB,EAAY,GAC5B,EAAc,EAAO,MAAM,EAAK,YAAY,EAChD,IAAK,EACL,OAAO,KACP,IAAI,EAAQ,EAAK,cAAgB,EAAK,cAAc,EAAY,EAAE,EAAI,EAAY,GAClF,EAAQ,EAAQ,cAAgB,EAAQ,cAAc,CAAK,EAAI,EAC/D,IAAI,EAAO,EAAO,MAAM,EAAc,MAAM,EAC5C,MAAO,CAAE,MAAO,EAAO,KAAM,CAAK,GAKtC,IAAI,EAA4B,eAC5B,EAA4B,OAC5B,EAAmB,CACrB,OAAQ,eACR,YAAa,eACb,KAAM,mCACR,EACI,EAAmB,CACrB,IAAK,CAAC,MAAM,KAAK,CACnB,EACI,EAAuB,CACzB,OAAQ,WACR,YAAa,gBACb,KAAM,gBACR,EACI,EAAuB,CACzB,IAAK,CAAC,KAAM,KAAM,KAAM,IAAI,CAC9B,EACI,EAAqB,CACvB,OAAQ,mBACR,YAAa,sDACb,KAAM,4EACR,EACI,EAAqB,CACvB,OAAQ,CACR,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,KAAI,EAEJ,IAAK,CACL,MACA,MACA,QACA,OACA,QACA,QACA,QACA,OACA,MACA,MACA,MACA,KAAI,CAEN,EACI,EAAmB,CACrB,OAAQ,cACR,MAAO,2BACP,YAAa,kCACb,KAAM,0DACR,EACI,EAAmB,CACrB,OAAQ,CAAC,MAAM,MAAO,MAAO,MAAO,MAAO,MAAO,KAAK,EACvD,IAAK,CAAC,OAAO,OAAQ,OAAQ,QAAS,QAAS,OAAQ,OAAO,CAChE,EACI,EAAyB,CAC3B,IAAK,+EACP,EACI,EAAyB,CAC3B,IAAK,CACH,GAAI,WACJ,GAAI,WACJ,SAAU,aACV,KAAM,mBACN,QAAS,WACT,UAAW,kBACX,QAAS,UACT,MAAO,MACT,CACF,EACI,EAAQ,CACV,cAAe,EAAoB,CACjC,aAAc,EACd,aAAc,EACd,uBAAwB,CAAa,CAAC,EAAO,CAAC,OAAO,SAAS,EAAO,EAAE,EACzE,CAAC,EACD,IAAK,EAAa,CAChB,cAAe,EACf,kBAAmB,OACnB,cAAe,EACf,kBAAmB,KACrB,CAAC,EACD,QAAS,EAAa,CACpB,cAAe,EACf,kBAAmB,OACnB,cAAe,EACf,kBAAmB,MACnB,uBAAwB,CAAa,CAAC,EAAO,CAAC,OAAO,EAAQ,EAC/D,CAAC,EACD,MAAO,EAAa,CAClB,cAAe,EACf,kBAAmB,OACnB,cAAe,EACf,kBAAmB,KACrB,CAAC,EACD,IAAK,EAAa,CAChB,cAAe,EACf,kBAAmB,OACnB,cAAe,EACf,kBAAmB,KACrB,CAAC,EACD,UAAW,EAAa,CACtB,cAAe,EACf,kBAAmB,MACnB,cAAe,EACf,kBAAmB,KACrB,CAAC,CACH,EAGI,GAAS,CACX,KAAM,UACN,eAAgB,EAChB,WAAY,EACZ,eAAgB,EAChB,SAAU,EACV,MAAO,EACP,QAAS,CACP,aAAc,EACd,sBAAuB,CACzB,CACF,EAGA,OAAO,QAAU,EAAc,EAAc,CAAC,EAC9C,OAAO,OAAO,EAAG,CAAC,EAAG,CACnB,OAAQ,EAAc,EAAc,CAAC,GAAI,EACzC,OAAO,WAAa,MAAQ,IAAyB,OAAS,OAAI,EAAgB,MAAM,EAAG,CAAC,EAAG,CAC7F,OAAQ,EAAO,CAAC,CAAE,CAAC,IAOpB", "debugId": "B6299DEA31F3F74464756E2164756E21", "names": []}