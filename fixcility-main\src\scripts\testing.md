name: Test and Auto Merge to Main

on:
  push:
    branches: [testing]

jobs:
  run-tests:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Install Dependencies
        run: npm install

      - name: Run Unit Tests
        run: npm run test

  auto-merge:
    needs: run-tests
    if: success()
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v4

      - name: Create Pull Request to Main
        uses: peter-evans/create-pull-request@v6
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          base: main
          branch: testing
          title: '✅ Auto Merge: testing → main'
          body: |
            ✅ Unit tests passed on `testing`.
            🔀 Auto-merging changes to `main`.
          merge_method: squash
