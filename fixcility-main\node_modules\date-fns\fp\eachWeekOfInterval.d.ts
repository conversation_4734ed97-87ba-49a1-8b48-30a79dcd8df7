export declare const eachWeekOfInterval: import("./types.js").FPFn1<
  import("../eachWeekOfInterval.js").EachWeekOfIntervalResult<
    import("../fp.js").Interval<
      import("../fp.js").DateArg<Date>,
      import("../fp.js").DateArg<Date>
    >,
    | import("../eachWeekOfInterval.js").EachWeekOfIntervalOptions<Date>
    | undefined
  >,
  import("../fp.js").Interval<
    import("../fp.js").DateArg<Date>,
    import("../fp.js").DateArg<Date>
  >
>;
