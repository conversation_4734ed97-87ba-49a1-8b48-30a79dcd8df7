// src/context/auth-context.tsx
import {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { useMutation } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
// import { loginService } from "@/services/api/auth";

type AuthContextType = {
  isAuthenticated: boolean;
  login: (email: string, password: string) => void;
  logout: () => void;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {

  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const token = localStorage.getItem("token");
    setIsAuthenticated(!!token);
  }, []);

  const mutation = useMutation({
    mutationFn: async ({ email, password }: { email: string; password: string }) => {
      // return await loginService({ email, password });
    },
    onSuccess: (data) => {
      // localStorage.setItem("token", data?.data?.accessToken);
      // localStorage.setItem("user", JSON.stringify(data?.data));
      setIsAuthenticated(true);
      toast.success("Login successful");
      navigate("/", { replace: true });
    },
    onError: (error: any) => {
      const msg =
        error?.response?.data?.message ||
        "Invalid credentials. Please try again.";
      toast.error(msg);
    },
  });

  const login = (email: string, password: string) => {
    mutation.mutate({ email, password });
  };

  const logout = () => {
    localStorage.removeItem("token");
    setIsAuthenticated(false);
     toast.success("Logged out successfully");
    navigate("/login");
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
