{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "strict": true, "jsx": "react-jsx", "baseUrl": "./src", "paths": {"@/*": ["*"], "components/*": ["components/*"], "utils/*": ["lib/utils/*"], "ui/*": ["components/ui/*"], "lib/*": ["lib/*"], "hooks/*": ["hooks/*"]}, "types": ["vite/client", "vitest"]}, "include": ["src"]}