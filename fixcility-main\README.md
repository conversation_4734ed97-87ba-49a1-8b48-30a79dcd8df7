# 🛠️ React + Vite + TypeScript + Tailwind CSS Template

This is a **starter template** for building scalable and maintainable front-end applications using:

- ⚛️ React 19 (with TypeScript)
- ⚡ Vite (blazing fast build tool)
- 💨 Tailwind CSS (utility-first styling)
- 🌐 TanStack React Query (for async data fetching)
- 🪝 Custom Fetch Wrapper (`fetchClient.ts` with interceptor-like logic)
- 📁 Clean folder structure with feature-based modules

---

## 🚀 Getting Started

### 1. Clone this Template

#### Option 1: From GitHub UI

- Click the `Use this template` button (top-right)
- Create a new repository from this template

#### Option 2: Manual via CLI

```bash
git clone https://github.com/<your-username>/react-vite-ts-template.git my-app
cd my-app
rm -rf .git
git init
git remote add origin https://github.com/<your-username>/my-app.git
git add .
git commit -m "init from template"
git push -u origin main
```

---

### 2. Install Dependencies

```bash
npm install
```

---

### 3. Start Dev Server

```bash
npm run dev
```

---

## 🔧 Environment Variables

Create a `.env` file in the root:

```
VITE_API_BASE_URL=https://api.example.com
```

---

## 📦 Folder Structure

```
src/
├── components/      # UI & Layout components
├── features/        # Feature-based modular code (auth, products...)
├── hooks/           # Reusable custom hooks
├── lib/             # fetchClient, helper libs
├── pages/           # Route-based pages
├── routes/          # Centralized route config
├── store/           # Optional state (not using Redux here)
├── styles/          # Global Tailwind styles
├── types/           # Global TypeScript declarations
└── utils/           # Utilities and constants
```

---

## 🧠 Features

- Fetch wrapper (`fetchClient.ts`) with token-based headers
- React Query integration
- Scalable project layout with feature folders
- Aliases like `@`, `@/components`, etc.
- ShadCN `components.json` config ready

---

## 🏁 Deployment

Build your app with:

```bash
npm run build
```

Then deploy the `dist/` folder to any static host (Vercel, Netlify, etc.)

---