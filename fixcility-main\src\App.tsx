import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { TooltipProvider } from "components/ui/tooltip";
import { AuthProvider } from "context/AuthContext";
import { BrowserRouter, Routes } from "react-router-dom";
import { AnimatePresence } from "framer-motion";
import { Toaster as Sonner } from "components/ui/sonner";
import { Toaster } from "components/ui/toaster";

const queryClient = new QueryClient();

const App = () => (
    <QueryClientProvider client={queryClient}>
        <TooltipProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter>
                <AuthProvider>
                    <AnimatePresence mode="wait">
                        <Routes>
                            {/* Authentication Routes */}
                            {/* <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} /> */}

                        </Routes>
                    </AnimatePresence>
                </AuthProvider>
            </BrowserRouter>
        </TooltipProvider>
    </QueryClientProvider>
)

export default App;