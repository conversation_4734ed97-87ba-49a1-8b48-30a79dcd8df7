name: Deploy Vite React HRMS UI to AWS

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Setup SSH
      run: |
        mkdir -p ~/.ssh
        echo "${{ secrets.PRIVATE_KEY }}" > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa
        ssh-keyscan -p ${{ secrets.PORT }} ${{ secrets.HOST }} >> ~/.ssh/known_hosts

    - name: Build and deploy Vite React app
      run: |
        ssh -i ~/.ssh/id_rsa -p ${{ secrets.PORT }} ${{ secrets.USERNAME }}@${{ secrets.HOST }} << 'EOF'
          echo "✅ Connected to server"

          echo "📁 Cloning project fresh"
          rm -rf /home/<USER>/React/Conprg-HRMS
          git clone https://<EMAIL>/Conprg-APPS/conprg_hrms.git 

          cd /var/React-Apps/HRMS-UI

          echo "📦 Installing dependencies"
          npm install

          echo "⚙️ Building app"
          npm run build

          echo "🧹 Cleaning old deployment"
          sudo rm -rf /var/React-Apps/HRMS/*

          echo "🚀 Copying new build to live folder"
          sudo cp -r dist/* /var/React-Apps/HRMS/

          echo "✅ Deployment completed"
        EOF